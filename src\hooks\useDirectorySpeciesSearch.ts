import { ref } from 'vue';
import { getSpecies } from '/@/api/screen';
import { createQuery } from '/@/screen/utils/query';

export interface DirectorySpecies {
  name: string;
  eventType?: number;
  [key: string]: any;
}

/**
 * 目录物种搜索 Hook
 * 使用 getSpecies 接口进行物种搜索，用于下拉框选择
 */
export function useDirectorySpeciesSearch() {
  const directorySpeciesOptions = ref<DirectorySpecies[]>([]);
  const searchLoading = ref(false);
  const searchError = ref<string | null>(null);

  /**
   * 搜索目录物种
   * @param name 物种名称关键字
   * @param eventTypeOrTypes 事件类型 - 可以是单个值、数组，或不传
   * @returns Promise<DirectorySpecies[]> 搜索结果
   *
   * @example
   * // 基础搜索
   * searchDirectorySpecies('老虎')
   *
   * // 单个事件类型
   * searchDirectorySpecies('老虎', 1)
   *
   * // 多个事件类型
   * searchDirectorySpecies('老虎', [1, 2])
   */
  const searchDirectorySpecies = async (
    name: string,
    eventTypeOrTypes?: number | string | (number | string)[]
  ): Promise<DirectorySpecies[]> => {
    if (!name || name.trim().length === 0) {
      return [];
    }

    searchLoading.value = true;
    searchError.value = null;

    try {
      const query = createQuery({ name: name.trim() });

      // 智能处理 eventType 参数
      if (eventTypeOrTypes !== undefined) {
        let processedEventTypes: number[] | undefined;

        if (Array.isArray(eventTypeOrTypes)) {
          // 处理数组：过滤掉 'all' 和非数字值
          const numericTypes = eventTypeOrTypes.filter(
            (type): type is number => typeof type === 'number'
          );
          if (numericTypes.length > 0 && !eventTypeOrTypes.includes('all')) {
            processedEventTypes = numericTypes;
          }
        } else if (eventTypeOrTypes !== 'all' && typeof eventTypeOrTypes === 'number') {
          // 处理单个数值
          processedEventTypes = [eventTypeOrTypes];
        }
        // 如果是 'all' 或其他情况，不设置 eventTypes

        if (processedEventTypes && processedEventTypes.length > 0) {
          query.eventTypes = processedEventTypes;
        }
      }

      const response = await getSpecies(query as any);
      const results = response.payload || [];

      // 更新选项列表
      directorySpeciesOptions.value = results;

      return results;
    } catch (error) {
      console.error('搜索目录物种失败:', error);
      searchError.value = '搜索目录物种失败';
      directorySpeciesOptions.value = [];
      return [];
    } finally {
      searchLoading.value = false;
    }
  };

  /**
   * 清空搜索结果
   */
  const clearDirectorySearchResults = (): void => {
    directorySpeciesOptions.value = [];
    searchError.value = null;
  };

  /**
   * 重置搜索状态
   */
  const resetDirectorySearchState = (): void => {
    directorySpeciesOptions.value = [];
    searchLoading.value = false;
    searchError.value = null;
  };

  return {
    // 状态
    directorySpeciesOptions,
    searchLoading,
    searchError,

    // 方法
    searchDirectorySpecies,
    clearDirectorySearchResults,
    resetDirectorySearchState,
  };
}
