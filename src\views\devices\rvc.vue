<template>
	<div class="rvc layout-pd">
		<div class="video-box">
			<div class="title">实时视频</div>
			<div class="video">
				<div ref="container"></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted,
	onUnmounted,
	watch,
	computed,
	nextTick,
	defineAsyncComponent,
	onActivated,
	onDeactivated,
} from 'vue';
import { useRoute } from 'vue-router';
import 'custom-vue-scrollbar/dist/style.css';

const SpeciesDialog = defineAsyncComponent(
	() => import('/@/views/monitorEvents/speciesDialog.vue')
);

const route = useRoute();
onActivated(() => {});
onDeactivated(() => {});

onMounted(() => {});
onUnmounted(() => {});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */
.rvc {
	display: flex;
	.video-box {
		flex: 1;
		// width: vw(1220);
		margin-right: vw(40);
		.video {
			position: relative;
			height: vh(686);
			background: #000;
			margin-bottom: vh(22);
			.record-state {
				position: absolute;
				top: vh(15);
				left: 50%;
				z-index: 9999999;
				transform: translateX(-50%);
				border-radius: vh(16);
				background-color: rgba(0, 0, 0, 0.3);
				width: vw(189);
				height: vh(32);
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				img {
					width: vw(22);
					margin-right: vw(16);
				}
				.record-duration {
					color: rgba(255, 255, 255, 1);
					font-size: 18px;
				}
			}
		}
		.toolbar-btns {
			height: vh(35);
			display: flex;
			justify-content: space-between;
			margin-bottom: vh(22);
			.toolbar-left {
				height: 100%;
				:deep(.el-button) {
					border-color: #ff0000;
				}
			}
			.toolbar-right {
				display: flex;
				justify-content: flex-end;
				height: 100%;
				:deep(.el-button) {
					border-color: rgba(126, 211, 33, 1);
				}
			}
		}

		.select {
			display: flex;
			flex-direction: column;
			height: vh(144);

			&:deep(.el-radio) {
				height: vh(38) !important;
				.el-radio__inner {
					background-color: #fff;
					&::after {
						width: 8px;
						height: 8px;
						background-color: rgba(126, 211, 33, 1);
					}
					&:hover {
						border-color: rgba(126, 211, 33, 1);
					}
				}
				.el-radio__input.is-checked .el-radio__inner {
					border-color: rgba(126, 211, 33, 1);
				}
				.el-radio__input.is-checked + .el-radio__label {
					color: rgba(126, 211, 33, 1) !important;
				}
			}
		}
	}
	.result {
		width: vw(360);
		.file-container {
			height: vh(280);
			margin-bottom: vh(10);
			.file {
				height: calc(100% - vh(45));
				overflow-y: auto;
				&::-webkit-scrollbar-thumb {
					background-color: #999;
				}
				video {
					width: 100%;
					height: 100%;
					vertical-align: middle;
				}
			}
		}
		.recognition-button-box {
			display: flex;
			margin-top: vh(6);
		}
		.jc-sb {
			justify-content: space-between;
		}
		.jc-end {
			justify-content: end;
		}
		.recognition {
			height: vh(580);
			overflow: auto;
		}
	}

	.title {
		height: vh(25);
		line-height: vh(25);
		margin: vh(10) 0;
		color: #333333;
		font-size: vw(18);
		font-weight: 700;
		&::before {
			margin-right: 10px;
			display: inline-block;
			content: '';
			width: 6px;
			height: 16px;
			background: var(--el-color-primary);
			vertical-align: middle;
			border-radius: 4px;
			margin-right: 5px;
			position: relative;
			bottom: 1.5px;
		}
		&::before {
			margin-right: 10px;
		}
		&.recoging::before {
			animation: loading-rotate 3s linear infinite;
		}
	}
	.card {
		border-radius: 4px;
		background-color: rgba(238, 238, 238, 0.34);
		padding: vh(10);
	}
	.recognition-button {
		width: vw(129);
		letter-spacing: 1px;
		font-size: 16px;
		img {
			width: vw(20);
			margin-right: vw(8);
		}
	}
}

:deep(.jessibuca-container) {
	.jessibuca-recording {
		position: absolute;
		left: 50%;
		top: 5px;
		width: vw(189);
		height: vh(32);
		transform: translateX(-50%);
		justify-content: space-around;
		align-items: center;
		background: rgba(0, 0, 0, 0.3);
		border-radius: vh(16);
		z-index: 1;
	}
	.jessibuca-recording-red-point {
		width: 12px;
		height: 12px;
		background: #ff4747;
	}
	.jessibuca-recording-time {
		font-size: 16px;
		font-weight: 500;
		color: #fff;
	}
	.jessibuca-icon-recordStop {
		width: 21px;
		height: 21px;
		background: url('/src/assets/sd/rvc/end-record.png') no-repeat;
		background-size: cover;
	}

	.jessibuca-controls {
		background-color: rgba(22, 22, 22, 0.7);
	}
}

.result-pictures-item {
	overflow: hidden;
	margin-bottom: 10px;
	position: relative;
	& > .el-image {
		width: 100%;
		// padding-top: 75% !important;
		& > img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			opacity: 0.5;
			transition: all 0.3s ease;
			cursor: pointer;
		}
		&:hover > img {
			transform: scale(1.05);
		}
		&.active > img {
			opacity: 1;
		}
	}
	.recResults-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.6);
		padding: 5px;
		& > span:hover {
			cursor: pointer;
			color: var(--el-color-primary);
		}
	}
	.status {
		position: absolute;
		width: 50px;
		height: 50px;
		border-radius: 50%;
		right: -25px;
		top: -25px;
		background-color: rgba(0, 0, 0, 0.6);
		.el-icon {
			position: absolute;
			left: 7px;
			bottom: 7px;
			color: #fff;
			font-size: 16px;
		}
		.el-icon.loading {
			color: #fff;
			animation: loading-rotate 3s linear infinite;
		}
		.el-icon.success {
			color: var(--el-color-success-light-3);
		}
	}
	&:last-child {
		margin-bottom: 0;
	}
}

:deep() {
	.el-button.is-disabled,
	.el-button.is-disabled:focus,
	.el-button.is-disabled:hover {
		border-color: var(--el-button-disabled-border-color) !important;
	}
}
</style>

<style lang="scss">
.handle-tooltip {
	padding: 0;
	.handle {
		padding: 15px 20px;
		&-keys {
			margin: 0 auto;
			width: vw(120);
			height: vw(120);
			background: url('/src/assets/sd/rvc/handle-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: relative;
			.key {
				position: absolute;
				cursor: pointer;
				img {
					width: 100%;
					height: 100%;
					vertical-align: middle;
					transform-origin: center;
				}
				&:hover img {
					transform: scale(1.05);
				}
			}
			.key.top {
				width: vw(28);
				top: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.bottom {
				width: vw(28);
				bottom: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.left {
				height: vw(28);
				left: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.right {
				height: vw(28);
				right: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.motion {
				width: vw(44);
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
			.key.add {
				width: vw(24);
				right: 0;
				bottom: 0;
			}
			.key.reduce {
				width: vw(24);
				left: 0;
				bottom: 0;
			}
		}
	}
}
</style>
